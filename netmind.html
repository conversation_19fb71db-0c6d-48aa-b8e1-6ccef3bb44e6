<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetMind AI Image Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            color: #ffffff;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 8s ease-in-out infinite alternate;
        }

        @keyframes gradientShift {
            0% { filter: hue-rotate(0deg); }
            100% { filter: hue-rotate(45deg); }
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .control-panel {
            position: sticky;
            top: 20px;
            height: fit-content;
        }

        .panel-section {
            margin-bottom: 25px;
            padding: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 15px 18px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: rgba(120, 119, 198, 0.5);
            box-shadow: 0 0 20px rgba(120, 119, 198, 0.2);
            background: rgba(255, 255, 255, 0.08);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .model-info {
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.2), rgba(255, 119, 198, 0.2));
            padding: 15px;
            border-radius: 12px;
            margin-top: 10px;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .file-upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            padding: 30px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.02);
            position: relative;
            overflow: hidden;
        }

        .file-upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .file-upload-area:hover {
            border-color: rgba(120, 119, 198, 0.6);
            background: rgba(120, 119, 198, 0.05);
            transform: translateY(-2px);
        }

        .file-upload-area:hover::before {
            left: 100%;
        }

        .file-upload-area.dragover {
            border-color: rgba(120, 119, 198, 0.8);
            background: rgba(120, 119, 198, 0.1);
            transform: scale(1.02);
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 14px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            margin-left: 15px;
        }

        .btn-secondary:hover {
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.4);
        }

        .btn-generate {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            width: 100%;
            padding: 20px;
            font-size: 18px;
            margin-bottom: 15px;
        }

        .btn-generate:hover {
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 30px;
            margin: 20px 0;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-panel {
            min-height: 600px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 0 5px;
        }

        .results-header h2 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #ffffff;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
        }

        .image-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .image-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.1), rgba(255, 119, 198, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .image-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(120, 119, 198, 0.3);
        }

        .image-card:hover::before {
            opacity: 1;
        }

        .image-card img {
            width: 100%;
            height: 280px;
            object-fit: cover;
            border-radius: 16px;
            margin-bottom: 15px;
            transition: transform 0.3s ease;
        }

        .image-card:hover img {
            transform: scale(1.05);
        }

        .image-info {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .image-info strong {
            color: rgba(255, 255, 255, 0.9);
            display: inline-block;
            min-width: 60px;
        }

        .image-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 10px 16px;
            font-size: 12px;
            flex: 1;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .error, .success {
            padding: 20px 25px;
            border-radius: 16px;
            margin: 20px 0;
            font-weight: 500;
            border: 1px solid;
            backdrop-filter: blur(20px);
        }

        .error {
            background: rgba(255, 59, 48, 0.1);
            color: #ff6b6b;
            border-color: rgba(255, 59, 48, 0.3);
        }

        .success {
            background: rgba(52, 199, 89, 0.1);
            color: #4ecdc4;
            border-color: rgba(52, 199, 89, 0.3);
        }

        .history-section {
            margin-top: 40px;
            padding: 30px;
        }

        .history-item {
            background: rgba(255, 255, 255, 0.03);
            padding: 20px;
            border-radius: 16px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
        }

        .history-item:hover {
            background: rgba(255, 255, 255, 0.06);
            border-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .hidden {
            display: none !important;
        }

        .preview-images {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .preview-images img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .preview-images img:hover {
            transform: scale(1.1);
        }

        /* Lightbox Styles */
        .lightbox {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }

        .lightbox.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .lightbox-content {
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            animation: slideIn 0.4s ease;
        }

        @keyframes slideIn {
            from { transform: scale(0.8) translateY(50px); opacity: 0; }
            to { transform: scale(1) translateY(0); opacity: 1; }
        }

        .lightbox-image {
            width: 100%;
            height: auto;
            max-height: 70vh;
            object-fit: contain;
            border-radius: 16px;
            margin-bottom: 20px;
        }

        .lightbox-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .lightbox-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .lightbox-close {
            position: absolute;
            top: -15px;
            right: -15px;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lightbox-close:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 350px 1fr;
                gap: 25px;
            }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .control-panel {
                position: static;
            }

            .header h1 {
                font-size: 2.5rem;
            }

            .container {
                padding: 15px;
            }

            .image-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 20px;
            }

            .lightbox-content {
                margin: 20px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 2rem;
            }

            .image-grid {
                grid-template-columns: 1fr;
            }

            .btn-secondary {
                margin-left: 0;
                margin-top: 10px;
            }

            .image-actions {
                flex-direction: column;
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
        }

        /* Input placeholder styling */
        ::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        /* Selection styling */
        ::selection {
            background: rgba(120, 119, 198, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="glass header">
            <h1>✨ NetMind AI Studio</h1>
            <p>Generate stunning AI images with advanced neural networks</p>
        </div>

        <div class="main-grid">
            <div class="control-panel">
                <div class="glass panel-section">
                    <div class="form-group">
                        <label for="apiKey">🔑 API Key</label>
                        <input type="password" id="apiKey" placeholder="Enter your NetMind AI API key">
                        <button class="btn btn-secondary" onclick="saveApiKey()" style="margin-top: 10px; width: 100%; margin-left: 0;">Save Key</button>
                    </div>
                </div>

                <div class="glass panel-section">
                    <div class="form-group">
                        <label for="model">🤖 Model</label>
                        <select id="model" onchange="updateModelInfo()">
                            <optgroup label="🎨 Text-to-Image">
                                <option value="black-forest-labs/FLUX.1-schnell">FLUX.1 Schnell</option>
                                <option value="stabilityai/stable-diffusion-3.5-large">Stable Diffusion 3.5 Large</option>
                                <option value="runwayml/stable-diffusion-v1-5">Stable Diffusion v1.5</option>
                            </optgroup>
                            <optgroup label="✏️ Image Editing">
                                <option value="black-forest-labs/FLUX.1-Depth-dev">FLUX.1 Depth</option>
                                <option value="black-forest-labs/FLUX.1-Canny-dev">FLUX.1 Canny</option>
                                <option value="black-forest-labs/FLUX.1-Fill-dev">FLUX.1 Fill</option>
                            </optgroup>
                            <optgroup label="🔄 Image Variation">
                                <option value="black-forest-labs/FLUX.1-Redux-dev">FLUX.1 Redux</option>
                            </optgroup>
                        </select>
                        <div id="modelInfo" class="model-info"></div>
                    </div>

                    <div class="form-group">
                        <label for="prompt">💭 Prompt</label>
                        <textarea id="prompt" placeholder="Describe your vision in detail..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="size">📐 Size</label>
                        <select id="size">
                            <option value="1024x1024">1024×1024 (Square)</option>
                            <option value="1152x896">1152×896 (Landscape)</option>
                            <option value="896x1152">896×1152 (Portrait)</option>
                            <option value="1216x832">1216×832 (Wide)</option>
                            <option value="832x1216">832×1216 (Tall)</option>
                        </select>
                    </div>
                </div>

                <div id="imageUploadSection" class="glass panel-section hidden">
                    <div class="form-group">
                        <label>🖼️ Input Image</label>
                        <div class="file-upload-area" onclick="document.getElementById('imageFile').click()">
                            <input type="file" id="imageFile" accept="image/*" style="display: none;" onchange="handleImageUpload(this)">
                            <p>📁 Click or drag & drop an image</p>
                            <div id="imagePreview" class="preview-images"></div>
                        </div>
                    </div>
                </div>

                <div id="maskUploadSection" class="glass panel-section hidden">
                    <div class="form-group">
                        <label>🎭 Mask Image</label>
                        <div class="file-upload-area" onclick="document.getElementById('maskFile').click()">
                            <input type="file" id="maskFile" accept="image/*" style="display: none;" onchange="handleMaskUpload(this)">
                            <p>📁 Click or drag & drop a mask</p>
                            <div id="maskPreview" class="preview-images"></div>
                        </div>
                    </div>
                </div>

                <div class="glass panel-section">
                    <button class="btn btn-generate" onclick="generateImage()">🚀 Generate Image</button>
                    <button class="btn btn-secondary" onclick="clearAll()" style="width: 100%; margin-left: 0;">🧹 Clear All</button>
                </div>

                <div class="loading glass panel-section" id="loading">
                    <div class="spinner"></div>
                    <p>Creating your masterpiece...</p>
                </div>

                <div id="error" class="error hidden"></div>
                <div id="success" class="success hidden"></div>
            </div>

            <div class="results-panel">
                <div class="glass" style="padding: 30px;">
                    <div class="results-header">
                        <h2>🎨 Generated Images</h2>
                    </div>
                    <div id="imageGrid" class="image-grid"></div>
                </div>
            </div>
        </div>

        <div class="glass history-section">
            <h2 style="margin-bottom: 25px;">📚 Generation History</h2>
            <div id="historyList"></div>
        </div>
    </div>

    <!-- Lightbox -->
    <div id="lightbox" class="lightbox" onclick="closeLightbox(event)">
        <div class="lightbox-content" onclick="event.stopPropagation()">
            <button class="lightbox-close" onclick="closeLightbox()">×</button>
            <img id="lightboxImage" class="lightbox-image" src="" alt="">
            <div id="lightboxInfo" class="lightbox-info"></div>
            <div class="lightbox-actions">
                <button class="btn" onclick="downloadLightboxImage()">📥 Download</button>
                <button class="btn btn-secondary" onclick="closeLightbox()">Close</button>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'https://api.netmind.ai/inference-api/openai/v1';
        const STORAGE_KEY = 'netmind_api_key';
        const HISTORY_KEY = 'netmind_generation_history';

        // Global variables for lightbox
        let currentLightboxData = null;

        // Model information
        const modelInfo = {
            'black-forest-labs/FLUX.1-schnell': '⚡ Ultra-fast text-to-image generation with excellent quality-speed balance.',
            'stabilityai/stable-diffusion-3.5-large': '🎯 Premium text-to-image with superior prompt understanding and detail.',
            'runwayml/stable-diffusion-v1-5': '🔰 Classic and reliable text-to-image model, perfect for general use.',
            'black-forest-labs/FLUX.1-Depth-dev': '🏔️ Advanced image editing using depth information. No mask required.',
            'black-forest-labs/FLUX.1-Canny-dev': '✏️ Precise image editing using edge detection. No mask required.',
            'black-forest-labs/FLUX.1-Fill-dev': '🎨 Professional inpainting for filling specific areas. Mask required.',
            'black-forest-labs/FLUX.1-Redux-dev': '🔄 Create stunning variations of existing images.'
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadApiKey();
            loadHistory();
            updateModelInfo();
            setupDragAndDrop();
            setupKeyboardShortcuts();
        });

        // Keyboard shortcuts
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        generateImage();
                    }
                }
                if (e.key === 'Escape') {
                    closeLightbox();
                }
            });
        }

        // API Key management
        function saveApiKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            if (apiKey) {
                setCookie(STORAGE_KEY, apiKey, 30);
                showSuccess('🔑 API Key saved successfully!');
            } else {
                showError('❌ Please enter a valid API key.');
            }
        }

        function loadApiKey() {
            const savedKey = getCookie(STORAGE_KEY);
            if (savedKey) {
                document.getElementById('apiKey').value = savedKey;
            }
        }

        // Cookie management
        function setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
        }

        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }

        // Model management
        function updateModelInfo() {
            const model = document.getElementById('model').value;
            const infoDiv = document.getElementById('modelInfo');
            const imageUploadSection = document.getElementById('imageUploadSection');
            const maskUploadSection = document.getElementById('maskUploadSection');

            infoDiv.textContent = modelInfo[model] || 'Model information not available.';

            // Show/hide upload sections based on model type
            const textToImageModels = [
                'black-forest-labs/FLUX.1-schnell',
                'stabilityai/stable-diffusion-3.5-large',
                'runwayml/stable-diffusion-v1-5'
            ];

            const needsImage = !textToImageModels.includes(model);
            const needsMask = model === 'black-forest-labs/FLUX.1-Fill-dev';

            imageUploadSection.classList.toggle('hidden', !needsImage);
            maskUploadSection.classList.toggle('hidden', !needsMask);
        }

        // File upload handling
        function setupDragAndDrop() {
            const uploadAreas = document.querySelectorAll('.file-upload-area');
            
            uploadAreas.forEach(area => {
                area.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                area.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                area.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const input = this.querySelector('input[type="file"]');
                        input.files = files;
                        
                        if (input.id === 'imageFile') {
                            handleImageUpload(input);
                        } else if (input.id === 'maskFile') {
                            handleMaskUpload(input);
                        }
                    }
                });
            });
        }

        function handleImageUpload(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('imagePreview');
                    preview.innerHTML = `<img src="${e.target.result}" alt="Input image">`;
                };
                reader.readAsDataURL(file);
            }
        }

        function handleMaskUpload(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('maskPreview');
                    preview.innerHTML = `<img src="${e.target.result}" alt="Mask image">`;
                };
                reader.readAsDataURL(file);
            }
        }

        // Image generation
        async function generateImage() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const model = document.getElementById('model').value;
            const prompt = document.getElementById('prompt').value.trim();
            const size = document.getElementById('size').value;

            if (!apiKey) {
                showError('🔑 Please enter your NetMind AI API key.');
                return;
            }

            if (!prompt) {
                showError('💭 Please enter a prompt.');
                return;
            }

            // Check if model requires input image
            const textToImageModels = [
                'black-forest-labs/FLUX.1-schnell',
                'stabilityai/stable-diffusion-3.5-large',
                'runwayml/stable-diffusion-v1-5'
            ];

            const needsImage = !textToImageModels.includes(model);
            const imageFile = document.getElementById('imageFile').files[0];

            if (needsImage && !imageFile) {
                showError('🖼️ This model requires an input image.');
                return;
            }

            showLoading(true);
            hideMessages();

            try {
                let response;
                
                if (textToImageModels.includes(model)) {
                    // Text-to-image generation
                    response = await fetch(`${API_BASE_URL}/images/generations`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: JSON.stringify({
                            model: model,
                            prompt: prompt,
                            size: size,
                            response_format: 'b64_json'
                        })
                    });
                } else if (model === 'black-forest-labs/FLUX.1-Redux-dev') {
                    // Image variation
                    const formData = new FormData();
                    formData.append('model', model);
                    formData.append('image', imageFile);
                    formData.append('response_format', 'b64_json');

                    response = await fetch(`${API_BASE_URL}/images/variations`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: formData
                    });
                } else {
                    // Image editing
                    const formData = new FormData();
                    formData.append('model', model);
                    formData.append('prompt', prompt);
                    formData.append('image', imageFile);
                    formData.append('response_format', 'b64_json');

                    // Add mask if required and available
                    const maskFile = document.getElementById('maskFile').files[0];
                    if (model === 'black-forest-labs/FLUX.1-Fill-dev' && maskFile) {
                        formData.append('mask', maskFile);
                    }

                    response = await fetch(`${API_BASE_URL}/images/edits`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: formData
                    });
                }

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`API request failed: ${response.status} ${response.statusText}${errorData.error ? ' - ' + errorData.error.message : ''}`);
                }

                const data = await response.json();
                
                if (data.data && data.data.length > 0) {
                    const imageBase64 = data.data[0].b64_json;
                    const imageUrl = `data:image/png;base64,${imageBase64}`;
                    
                    // Add to display
                    addImageToGrid(imageUrl, model, prompt, size);
                    
                    // Save to history
                    saveToHistory(model, prompt, imageUrl, size, new Date().toISOString());
                    
                    // Trigger download
                    downloadImage(imageBase64, `netmind_${Date.now()}.png`);
                    
                    showSuccess('✨ Image generated successfully!');
                } else {
                    throw new Error('No image data received from API.');
                }

            } catch (error) {
                console.error('Generation error:', error);
                showError(`❌ Failed to generate image: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        // UI Management
        function addImageToGrid(imageUrl, model, prompt, size) {
            const grid = document.getElementById('imageGrid');
            const timestamp = new Date().toLocaleString();
            const modelName = model.split('/')[1] || model;
            
            const imageCard = document.createElement('div');
            imageCard.className = 'image-card';
            imageCard.innerHTML = `
                <img src="${imageUrl}" alt="Generated image" onclick="openLightbox('${imageUrl}', '${modelName}', '${prompt.replace(/'/g, "\\'")}', '${size}', '${timestamp}')">
                <div class="image-info">
                    <div><strong>Model:</strong> ${modelName}</div>
                    <div><strong>Size:</strong> ${size}</div>
                    <div><strong>Prompt:</strong> ${prompt.substring(0, 80)}${prompt.length > 80 ? '...' : ''}</div>
                    <div><strong>Generated:</strong> ${timestamp}</div>
                </div>
                <div class="image-actions">
                    <button class="btn btn-small" onclick="downloadImageFromUrl('${imageUrl}', 'netmind_${Date.now()}.png')">📥 Download</button>
                    <button class="btn btn-small btn-secondary" onclick="this.parentElement.parentElement.remove()">🗑️ Remove</button>
                </div>
            `;
            
            grid.insertBefore(imageCard, grid.firstChild);
        }

        // Lightbox functionality
        function openLightbox(imageUrl, model, prompt, size, timestamp) {
            const lightbox = document.getElementById('lightbox');
            const lightboxImage = document.getElementById('lightboxImage');
            const lightboxInfo = document.getElementById('lightboxInfo');
            
            lightboxImage.src = imageUrl;
            lightboxInfo.innerHTML = `
                <div><strong>🤖 Model:</strong> ${model}</div>
                <div><strong>📐 Size:</strong> ${size}</div>
                <div><strong>💭 Prompt:</strong> ${prompt}</div>
                <div><strong>🕒 Generated:</strong> ${timestamp}</div>
            `;
            
            currentLightboxData = {
                url: imageUrl,
                filename: `netmind_${model}_${Date.now()}.png`
            };
            
            lightbox.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeLightbox(event) {
            if (event && event.target !== event.currentTarget) return;
            
            const lightbox = document.getElementById('lightbox');
            lightbox.classList.remove('active');
            document.body.style.overflow = 'auto';
            currentLightboxData = null;
        }

        function downloadLightboxImage() {
            if (currentLightboxData) {
                downloadImageFromUrl(currentLightboxData.url, currentLightboxData.filename);
            }
        }

        function downloadImage(base64Data, filename) {
            const link = document.createElement('a');
            link.href = `data:image/png;base64,${base64Data}`;
            link.download = filename;
            link.click();
        }

        function downloadImageFromUrl(imageUrl, filename) {
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = filename;
            link.click();
        }

        // History management
        function saveToHistory(model, prompt, imageUrl, size, timestamp) {
            let history = JSON.parse(localStorage.getItem(HISTORY_KEY) || '[]');
            history.unshift({
                model,
                prompt,
                imageUrl,
                size,
                timestamp
            });
            
            // Keep only last 50 items
            if (history.length > 50) {
                history = history.slice(0, 50);
            }
            
            localStorage.setItem(HISTORY_KEY, JSON.stringify(history));
            loadHistory();
        }

        function loadHistory() {
            const history = JSON.parse(localStorage.getItem(HISTORY_KEY) || '[]');
            const historyList = document.getElementById('historyList');
            
            historyList.innerHTML = '';
            
            if (history.length === 0) {
                historyList.innerHTML = '<div style="text-align: center; color: rgba(255,255,255,0.5); padding: 40px;">📝 No generation history yet. Create your first image!</div>';
                return;
            }
            
            history.forEach((item, index) => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                const modelName = item.model.split('/')[1] || item.model;
                historyItem.innerHTML = `
                    <div>
                        <div style="font-weight: 600; color: #4ecdc4; margin-bottom: 5px;">🤖 ${modelName}</div>
                        <div style="margin-bottom: 5px;">${item.prompt.substring(0, 100)}${item.prompt.length > 100 ? '...' : ''}</div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                            📐 ${item.size || 'N/A'} • 🕒 ${new Date(item.timestamp).toLocaleString()}
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-small" onclick="regenerateFromHistory(${index})">🔄 Regenerate</button>
                        <button class="btn btn-small btn-secondary" onclick="removeFromHistory(${index})">🗑️ Remove</button>
                    </div>
                `;
                historyList.appendChild(historyItem);
            });
        }

        function regenerateFromHistory(index) {
            const history = JSON.parse(localStorage.getItem(HISTORY_KEY) || '[]');
            const item = history[index];
            
            if (item) {
                document.getElementById('model').value = item.model;
                document.getElementById('prompt').value = item.prompt;
                if (item.size) {
                    document.getElementById('size').value = item.size;
                }
                updateModelInfo();
                showSuccess('🔄 Settings loaded from history!');
            }
        }

        function removeFromHistory(index) {
            let history = JSON.parse(localStorage.getItem(HISTORY_KEY) || '[]');
            history.splice(index, 1);
            localStorage.setItem(HISTORY_KEY, JSON.stringify(history));
            loadHistory();
            showSuccess('🗑️ Item removed from history!');
        }

        // Utility functions
        function showLoading(show) {
            const loading = document.getElementById('loading');
            const generateBtn = document.querySelector('.btn-generate');
            
            if (show) {
                loading.style.display = 'block';
                generateBtn.disabled = true;
                generateBtn.textContent = '⏳ Generating...';
            } else {
                loading.style.display = 'none';
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 Generate Image';
            }
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
            setTimeout(() => errorDiv.classList.add('hidden'), 6000);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success');
            successDiv.textContent = message;
            successDiv.classList.remove('hidden');
            setTimeout(() => successDiv.classList.add('hidden'), 4000);
        }

        function hideMessages() {
            document.getElementById('error').classList.add('hidden');
            document.getElementById('success').classList.add('hidden');
        }

        function clearAll() {
            document.getElementById('prompt').value = '';
            document.getElementById('imageFile').value = '';
            document.getElementById('maskFile').value = '';
            document.getElementById('imagePreview').innerHTML = '';
            document.getElementById('maskPreview').innerHTML = '';
            document.getElementById('imageGrid').innerHTML = '';
            hideMessages();
            showSuccess('🧹 All fields cleared!');
        }
    </script>
</body>
</html>