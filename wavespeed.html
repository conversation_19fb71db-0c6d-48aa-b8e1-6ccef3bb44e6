<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaveSpeed AI Studio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f1419 100%);
            min-height: 100vh;
            color: #ffffff;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            padding: 30px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1));
            z-index: -1;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #9333ea, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .panel-section {
            padding: 25px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #3b82f6;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #e2e8f0;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #ffffff;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .btn-generate {
            width: 100%;
            background: linear-gradient(135deg, #9333ea, #7c3aed);
            color: white;
            font-size: 16px;
            padding: 16px;
        }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(147, 51, 234, 0.4);
        }

        .btn-settings {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .model-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .model-toggle:hover {
            background: rgba(255, 255, 255, 0.08);
        }

        .model-toggle.enabled {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: #3b82f6;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        .main-content {
            padding: 30px;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .result-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .result-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .result-card-content {
            padding: 20px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 2000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: rgba(20, 20, 30, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .modal-header h2 {
            color: #3b82f6;
        }

        .close-btn {
            background: none;
            border: none;
            color: #e2e8f0;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: none;
        }

        .alert.success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }

        .alert.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .model-info {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 5px;
            font-style: italic;
        }

        .file-upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            padding: 30px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.02);
            position: relative;
            overflow: hidden;
        }

        .file-upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .file-upload-area:hover {
            border-color: rgba(59, 130, 246, 0.6);
            background: rgba(59, 130, 246, 0.05);
            transform: translateY(-2px);
        }

        .file-upload-area:hover::before {
            left: 100%;
        }

        .file-upload-area.dragover {
            border-color: rgba(59, 130, 246, 0.8);
            background: rgba(59, 130, 246, 0.1);
            transform: scale(1.02);
        }

        .preview-images {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .preview-images img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .preview-images img:hover {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }

        ::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <button class="btn-settings" onclick="openSettings()">⚙️</button>

    <div class="container">
        <div class="glass header">
            <h1>⚡ WaveSpeed AI Studio</h1>
            <p>Multi-model AI generation platform with lightning speed</p>
        </div>

        <div class="main-layout">
            <div class="sidebar">
                <div class="glass panel-section">
                    <div class="section-title">
                        🎯 Generation Settings
                    </div>
                    
                    <div class="form-group">
                        <label for="activeModel">Active Model</label>
                        <select id="activeModel" onchange="updateModelInfo()">
                            <!-- Models will be populated by JavaScript -->
                        </select>
                        <div id="modelInfo" class="model-info"></div>
                    </div>

                    <div class="form-group">
                        <label for="prompt">💭 Prompt</label>
                        <textarea id="prompt" placeholder="Describe what you want to generate..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="size">📐 Size</label>
                        <select id="size">
                            <option value="1024x1024">1024×1024 (Square)</option>
                            <option value="1152x896">1152×896 (Landscape)</option>
                            <option value="896x1152">896×1152 (Portrait)</option>
                            <option value="1216x832">1216×832 (Wide)</option>
                            <option value="832x1216">832×1216 (Tall)</option>
                        </select>
                    </div>

                    <div id="imageUploadSection" class="form-group" style="display: none;">
                        <label>🖼️ Input Image</label>
                        <div class="file-upload-area" onclick="document.getElementById('imageFile').click()">
                            <input type="file" id="imageFile" accept="image/*" style="display: none;" onchange="handleImageUpload(this)">
                            <p>📁 Click or drag & drop an image</p>
                            <div id="imagePreview" class="preview-images"></div>
                        </div>
                    </div>

                    <div id="maskUploadSection" class="form-group" style="display: none;">
                        <label>🎭 Mask Image</label>
                        <div class="file-upload-area" onclick="document.getElementById('maskFile').click()">
                            <input type="file" id="maskFile" accept="image/*" style="display: none;" onchange="handleMaskUpload(this)">
                            <p>📁 Click or drag & drop a mask</p>
                            <div id="maskPreview" class="preview-images"></div>
                        </div>
                    </div>

                    <button class="btn btn-generate" onclick="generateContent()">
                        ⚡ Generate
                    </button>
                </div>

                <div class="glass panel-section">
                    <div class="section-title">
                        📊 Quick Actions
                    </div>
                    <button class="btn btn-secondary" onclick="clearResults()" style="width: 100%; margin-bottom: 10px;">
                        🧹 Clear Results
                    </button>
                    <button class="btn btn-secondary" onclick="openSettings()" style="width: 100%;">
                        ⚙️ Settings
                    </button>
                </div>
            </div>

            <div class="main-content glass">
                <div class="section-title">
                    🎨 Generated Content
                </div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Generating your content...</p>
                </div>

                <div class="alert" id="alert"></div>

                <div class="results-grid" id="resultsGrid">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>⚙️ Settings</h2>
                <button class="close-btn" onclick="closeSettings()">&times;</button>
            </div>

            <div class="form-group">
                <label for="apiKey">🔑 API Key</label>
                <input type="password" id="apiKey" placeholder="Enter your WaveSpeed AI API key">
                <button class="btn btn-primary" onclick="saveApiKey()" style="margin-top: 10px; width: 100%;">
                    💾 Save API Key
                </button>
            </div>

            <div class="section-title" style="margin-top: 30px;">
                🤖 Available Models
            </div>
            <div id="modelsList">
                <!-- Model toggles will be populated here -->
            </div>

            <div style="margin-top: 30px;">
                <button class="btn btn-secondary" onclick="resetSettings()" style="width: 100%;">
                    🔄 Reset All Settings
                </button>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'https://api.wavespeed.ai/api/v3';
        const STORAGE_KEYS = {
            API_KEY: 'wavespeed_api_key',
            ENABLED_MODELS: 'wavespeed_enabled_models',
            RESULTS_HISTORY: 'wavespeed_results_history'
        };

        // Available models with their information
        const AVAILABLE_MODELS = {
            // FLUX Text-to-Image Models
            'wavespeed-ai/flux-1.1-pro': {
                name: 'FLUX 1.1 Pro',
                type: 'text-to-image',
                description: '� Latest FLUX model with superior quality and speed',
                category: 'FLUX',
                endpoint: 'images/generations'
            },
            'wavespeed-ai/flux-1.1-pro-ultra': {
                name: 'FLUX 1.1 Pro Ultra',
                type: 'text-to-image',
                description: '⚡ Ultra-high quality FLUX model',
                category: 'FLUX',
                endpoint: 'images/generations'
            },
            'wavespeed-ai/flux-dev': {
                name: 'FLUX Dev',
                type: 'text-to-image',
                description: '🎨 FLUX development model for creative generation',
                category: 'FLUX',
                endpoint: 'images/generations'
            },
            'wavespeed-ai/flux-dev-lora': {
                name: 'FLUX Dev LoRA',
                type: 'text-to-image',
                description: '🎯 FLUX with LoRA support for custom styles',
                category: 'FLUX',
                endpoint: 'images/generations'
            },
            'wavespeed-ai/flux-schnell': {
                name: 'FLUX Schnell',
                type: 'text-to-image',
                description: '⚡ Ultra-fast FLUX model',
                category: 'FLUX',
                endpoint: 'images/generations'
            },

            // Google Models
            'google/imagen4': {
                name: 'Google Imagen 4',
                type: 'text-to-image',
                description: '🌟 Google\'s flagship image generation model',
                category: 'Google',
                endpoint: 'images/generations'
            },
            'google/imagen3': {
                name: 'Google Imagen 3',
                type: 'text-to-image',
                description: '🎨 Google\'s advanced image model',
                category: 'Google',
                endpoint: 'images/generations'
            },
            'google/imagen3-fast': {
                name: 'Google Imagen 3 Fast',
                type: 'text-to-image',
                description: '⚡ Fast version of Imagen 3',
                category: 'Google',
                endpoint: 'images/generations'
            },

            // Ideogram Models
            'ideogram-ai/ideogram-v3-quality': {
                name: 'Ideogram V3 Quality',
                type: 'text-to-image',
                description: '� Highest quality Ideogram model',
                category: 'Ideogram',
                endpoint: 'images/generations'
            },
            'ideogram-ai/ideogram-v3-balanced': {
                name: 'Ideogram V3 Balanced',
                type: 'text-to-image',
                description: '⚖️ Balanced quality and speed',
                category: 'Ideogram',
                endpoint: 'images/generations'
            },
            'ideogram-ai/ideogram-v3-turbo': {
                name: 'Ideogram V3 Turbo',
                type: 'text-to-image',
                description: '� Fast Ideogram generation',
                category: 'Ideogram',
                endpoint: 'images/generations'
            },

            // Recraft Models
            'recraft-ai/recraft-v3': {
                name: 'Recraft V3',
                type: 'text-to-image',
                description: '🎨 Advanced design-focused model',
                category: 'Recraft',
                endpoint: 'images/generations'
            },
            'recraft-ai/recraft-20b': {
                name: 'Recraft 20B',
                type: 'text-to-image',
                description: '🔥 Large-scale design model',
                category: 'Recraft',
                endpoint: 'images/generations'
            },

            // ByteDance Models
            'bytedance/seedream-v3': {
                name: 'ByteDance Seedream V3',
                type: 'text-to-image',
                description: '🌟 State-of-the-art photorealistic generation',
                category: 'ByteDance',
                endpoint: 'images/generations'
            },

            // WAN Models
            'wan-2.1/text-to-image': {
                name: 'WAN 2.1 Text-to-Image',
                type: 'text-to-image',
                description: '🎯 Alibaba\'s advanced text-to-image model',
                category: 'WAN',
                endpoint: 'images/generations'
            },

            // Image-to-Image Models
            'wavespeed-ai/flux-redux-dev': {
                name: 'FLUX Redux Dev',
                type: 'image-to-image',
                description: '🔄 Create variations of existing images',
                category: 'FLUX',
                endpoint: 'images/variations'
            },
            'wavespeed-ai/flux-redux-pro': {
                name: 'FLUX Redux Pro',
                type: 'image-to-image',
                description: '🔄 Professional image variations',
                category: 'FLUX',
                endpoint: 'images/variations'
            },
            'wavespeed-ai/flux-fill-dev': {
                name: 'FLUX Fill Dev',
                type: 'image-to-image',
                description: '🎨 Professional inpainting with mask',
                category: 'FLUX',
                endpoint: 'images/edits'
            },
            'wavespeed-ai/flux-controlnet-union-pro-2.0': {
                name: 'FLUX ControlNet Union Pro',
                type: 'image-to-image',
                description: '🎯 Advanced image editing with control',
                category: 'FLUX',
                endpoint: 'images/edits'
            }
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            populateModels();
            loadResults();
            setupDragAndDrop();
        });

        // Settings management
        function loadSettings() {
            const apiKey = localStorage.getItem(STORAGE_KEYS.API_KEY);
            if (apiKey) {
                document.getElementById('apiKey').value = apiKey;
            }

            const enabledModels = JSON.parse(localStorage.getItem(STORAGE_KEYS.ENABLED_MODELS) || '{}');
            
            // Enable some models by default if none are set
            if (Object.keys(enabledModels).length === 0) {
                enabledModels['wavespeed-ai/flux-1.1-pro'] = true;
                enabledModels['wavespeed-ai/flux-dev'] = true;
                enabledModels['google/imagen4'] = true;
                enabledModels['ideogram-ai/ideogram-v3-quality'] = true;
                enabledModels['wavespeed-ai/flux-redux-dev'] = true;
                localStorage.setItem(STORAGE_KEYS.ENABLED_MODELS, JSON.stringify(enabledModels));
            }
        }

        function saveApiKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            if (apiKey) {
                localStorage.setItem(STORAGE_KEYS.API_KEY, apiKey);
                showAlert('🔑 API Key saved successfully!', 'success');
            } else {
                showAlert('❌ Please enter a valid API key.', 'error');
            }
        }

        function populateModels() {
            const enabledModels = JSON.parse(localStorage.getItem(STORAGE_KEYS.ENABLED_MODELS) || '{}');
            const modelsList = document.getElementById('modelsList');
            const activeModelSelect = document.getElementById('activeModel');

            // Clear existing options
            modelsList.innerHTML = '';
            activeModelSelect.innerHTML = '';

            // Group models by category
            const categories = {};
            Object.entries(AVAILABLE_MODELS).forEach(([modelId, model]) => {
                if (!categories[model.category]) {
                    categories[model.category] = [];
                }
                categories[model.category].push([modelId, model]);
            });

            // Create optgroups for each category
            Object.entries(categories).forEach(([category, models]) => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = `${getCategoryIcon(category)} ${category}`;

                models.forEach(([modelId, model]) => {
                    // Create toggle in settings
                    const toggleDiv = document.createElement('div');
                    toggleDiv.className = `model-toggle ${enabledModels[modelId] ? 'enabled' : ''}`;
                    toggleDiv.innerHTML = `
                        <div>
                            <div style="font-weight: 600;">${model.name}</div>
                            <div style="font-size: 12px; opacity: 0.7;">${model.description}</div>
                            <div style="font-size: 10px; opacity: 0.5; margin-top: 2px;">${model.type}</div>
                        </div>
                        <div class="toggle-switch ${enabledModels[modelId] ? 'active' : ''}" onclick="toggleModel('${modelId}')"></div>
                    `;
                    modelsList.appendChild(toggleDiv);

                    // Add to active model select if enabled
                    if (enabledModels[modelId]) {
                        const option = document.createElement('option');
                        option.value = modelId;
                        option.textContent = model.name;
                        optgroup.appendChild(option);
                    }
                });

                if (optgroup.children.length > 0) {
                    activeModelSelect.appendChild(optgroup);
                }
            });

            updateModelInfo();
        }

        function getCategoryIcon(category) {
            const icons = {
                'FLUX': '⚡',
                'Google': '🌟',
                'Ideogram': '🎯',
                'Recraft': '🎨',
                'ByteDance': '🚀',
                'WAN': '🔥'
            };
            return icons[category] || '🤖';
        }

        function toggleModel(modelId) {
            const enabledModels = JSON.parse(localStorage.getItem(STORAGE_KEYS.ENABLED_MODELS) || '{}');
            enabledModels[modelId] = !enabledModels[modelId];
            localStorage.setItem(STORAGE_KEYS.ENABLED_MODELS, JSON.stringify(enabledModels));
            populateModels();
        }

        function updateModelInfo() {
            const activeModel = document.getElementById('activeModel').value;
            const modelInfo = document.getElementById('modelInfo');
            const imageUploadSection = document.getElementById('imageUploadSection');
            const maskUploadSection = document.getElementById('maskUploadSection');

            if (activeModel && AVAILABLE_MODELS[activeModel]) {
                const model = AVAILABLE_MODELS[activeModel];
                modelInfo.textContent = model.description;

                // Show/hide upload sections based on model type
                const needsImage = model.type === 'image-to-image';
                const needsMask = activeModel === 'wavespeed-ai/flux-fill-dev';

                imageUploadSection.style.display = needsImage ? 'block' : 'none';
                maskUploadSection.style.display = needsMask ? 'block' : 'none';
            } else {
                modelInfo.textContent = 'Select a model to see information';
                imageUploadSection.style.display = 'none';
                maskUploadSection.style.display = 'none';
            }
        }

        function resetSettings() {
            if (confirm('Are you sure you want to reset all settings? This will clear your API key and model preferences.')) {
                Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key));
                document.getElementById('apiKey').value = '';
                loadSettings();
                populateModels();
                showAlert('🔄 Settings reset successfully!', 'success');
            }
        }

        // Modal management
        function openSettings() {
            document.getElementById('settingsModal').classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeSettings() {
            document.getElementById('settingsModal').classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // File upload handling
        function setupDragAndDrop() {
            const uploadAreas = document.querySelectorAll('.file-upload-area');

            uploadAreas.forEach(area => {
                area.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                area.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                area.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const input = this.querySelector('input[type="file"]');
                        input.files = files;

                        if (input.id === 'imageFile') {
                            handleImageUpload(input);
                        } else if (input.id === 'maskFile') {
                            handleMaskUpload(input);
                        }
                    }
                });
            });
        }

        function handleImageUpload(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('imagePreview');
                    preview.innerHTML = `<img src="${e.target.result}" alt="Input image">`;
                };
                reader.readAsDataURL(file);
            }
        }

        function handleMaskUpload(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('maskPreview');
                    preview.innerHTML = `<img src="${e.target.result}" alt="Mask image">`;
                };
                reader.readAsDataURL(file);
            }
        }

        // Content generation
        async function generateContent() {
            const apiKey = localStorage.getItem(STORAGE_KEYS.API_KEY);
            const activeModel = document.getElementById('activeModel').value;
            const prompt = document.getElementById('prompt').value.trim();
            const size = document.getElementById('size').value;

            if (!apiKey) {
                showAlert('🔑 Please set your API key in settings first.', 'error');
                return;
            }

            if (!activeModel) {
                showAlert('🤖 Please enable at least one model in settings.', 'error');
                return;
            }

            if (!prompt && AVAILABLE_MODELS[activeModel].type === 'text-to-image') {
                showAlert('💭 Please enter a prompt.', 'error');
                return;
            }

            const model = AVAILABLE_MODELS[activeModel];

            // Check if model requires input image
            const needsImage = model.type === 'image-to-image';
            const imageFile = document.getElementById('imageFile').files[0];

            if (needsImage && !imageFile) {
                showAlert('🖼️ This model requires an input image.', 'error');
                return;
            }

            const loading = document.getElementById('loading');
            loading.classList.add('active');

            try {
                // Step 1: Submit the task
                const taskResponse = await submitTask(apiKey, activeModel, prompt, size, imageFile);

                if (!taskResponse.ok) {
                    const errorData = await taskResponse.json().catch(() => ({}));
                    throw new Error(`Task submission failed: ${taskResponse.status} ${taskResponse.statusText}${errorData.message ? ' - ' + errorData.message : ''}`);
                }

                const taskData = await taskResponse.json();

                if (taskData.code !== 200 || !taskData.data || !taskData.data.id) {
                    throw new Error(`Task submission failed: ${taskData.message || 'Unknown error'}`);
                }

                const taskId = taskData.data.id;
                showAlert(`🚀 Task submitted! ID: ${taskId}. Waiting for completion...`, 'success');

                // Step 2: Poll for results
                const result = await pollForResult(apiKey, taskId);

                if (result.outputs && result.outputs.length > 0) {
                    const imageUrl = result.outputs[0];
                    addImageResult(imageUrl, activeModel, prompt, size);
                    showAlert('✨ Image generated successfully!', 'success');
                } else {
                    throw new Error('No image data received from API.');
                }

            } catch (error) {
                console.error('Generation error:', error);
                showAlert(`❌ Generation failed: ${error.message}`, 'error');
            } finally {
                loading.classList.remove('active');
            }
        }

        async function submitTask(apiKey, modelId, prompt, size, imageFile) {
            const model = AVAILABLE_MODELS[modelId];

            // Convert size format (e.g., "1024x1024" to "1:1")
            const aspectRatio = convertSizeToAspectRatio(size);

            if (model.type === 'text-to-image') {
                // Text-to-image generation
                return await fetch(`${API_BASE_URL}/${modelId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        aspect_ratio: aspectRatio,
                        output_format: 'jpg',
                        enable_base64_output: false
                    })
                });
            } else {
                // Image-to-image models would need different handling
                // For now, treat as text-to-image
                return await fetch(`${API_BASE_URL}/${modelId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        aspect_ratio: aspectRatio,
                        output_format: 'jpg',
                        enable_base64_output: false
                    })
                });
            }
        }

        function convertSizeToAspectRatio(size) {
            const sizeMap = {
                '1024x1024': '1:1',
                '1152x896': '9:7',
                '896x1152': '7:9',
                '1216x832': '3:2',
                '832x1216': '2:3'
            };
            return sizeMap[size] || '1:1';
        }

        async function pollForResult(apiKey, taskId, maxAttempts = 60, interval = 2000) {
            for (let attempt = 0; attempt < maxAttempts; attempt++) {
                try {
                    const response = await fetch(`${API_BASE_URL}/predictions/${taskId}/result`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${apiKey}`
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`Result query failed: ${response.status} ${response.statusText}`);
                    }

                    const data = await response.json();

                    if (data.code !== 200) {
                        throw new Error(`Result query failed: ${data.message || 'Unknown error'}`);
                    }

                    const result = data.data;

                    if (result.status === 'completed') {
                        return result;
                    } else if (result.status === 'failed') {
                        throw new Error(`Task failed: ${result.error || 'Unknown error'}`);
                    }

                    // Still processing, wait and try again
                    await new Promise(resolve => setTimeout(resolve, interval));

                } catch (error) {
                    if (attempt === maxAttempts - 1) {
                        throw error;
                    }
                    await new Promise(resolve => setTimeout(resolve, interval));
                }
            }

            throw new Error('Task timed out after maximum attempts');
        }

        function addImageResult(imageUrl, model, prompt, size) {
            const resultsGrid = document.getElementById('resultsGrid');
            const resultCard = document.createElement('div');
            resultCard.className = 'result-card';
            resultCard.innerHTML = `
                <img src="${imageUrl}" alt="Generated image" onclick="window.open('${imageUrl}', '_blank')">
                <div class="result-card-content">
                    <div style="font-weight: 600; margin-bottom: 8px;">${AVAILABLE_MODELS[model].name}</div>
                    <div style="font-size: 12px; opacity: 0.7; margin-bottom: 10px;">${size}</div>
                    <div style="font-size: 14px; margin-bottom: 15px;">${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}</div>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-primary" onclick="downloadImage('${imageUrl}', '${model}_${Date.now()}.png')" style="flex: 1;">📥 Download</button>
                        <button class="btn btn-secondary" onclick="this.closest('.result-card').remove()">🗑️</button>
                    </div>
                </div>
            `;
            resultsGrid.insertBefore(resultCard, resultsGrid.firstChild);
            saveResult('image', imageUrl, model, prompt, size);
        }

        function addTextResult(text, model, prompt) {
            const resultsGrid = document.getElementById('resultsGrid');
            const resultCard = document.createElement('div');
            resultCard.className = 'result-card';
            resultCard.innerHTML = `
                <div class="result-card-content">
                    <div style="font-weight: 600; margin-bottom: 8px;">${AVAILABLE_MODELS[model].name}</div>
                    <div style="font-size: 12px; opacity: 0.7; margin-bottom: 10px;">Text Generation</div>
                    <div style="font-size: 14px; margin-bottom: 10px; font-weight: 500;">Prompt:</div>
                    <div style="font-size: 13px; opacity: 0.8; margin-bottom: 15px;">${prompt}</div>
                    <div style="font-size: 14px; margin-bottom: 10px; font-weight: 500;">Response:</div>
                    <div style="font-size: 13px; line-height: 1.5; margin-bottom: 15px; max-height: 150px; overflow-y: auto;">${text}</div>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-primary" onclick="copyToClipboard('${text.replace(/'/g, "\\'")}')">📋 Copy</button>
                        <button class="btn btn-secondary" onclick="this.closest('.result-card').remove()">🗑️</button>
                    </div>
                </div>
            `;
            resultsGrid.insertBefore(resultCard, resultsGrid.firstChild);
            saveResult('text', text, model, prompt);
        }

        // Utility functions
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert ${type}`;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        function clearResults() {
            if (confirm('Are you sure you want to clear all results?')) {
                document.getElementById('resultsGrid').innerHTML = '';
                localStorage.removeItem(STORAGE_KEYS.RESULTS_HISTORY);
                showAlert('🧹 Results cleared!', 'success');
            }
        }

        function downloadImage(url, filename) {
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showAlert('📋 Copied to clipboard!', 'success');
            }).catch(() => {
                showAlert('❌ Failed to copy to clipboard', 'error');
            });
        }

        function saveResult(type, content, model, prompt, size = null) {
            const results = JSON.parse(localStorage.getItem(STORAGE_KEYS.RESULTS_HISTORY) || '[]');
            results.unshift({
                type,
                content,
                model,
                prompt,
                size,
                timestamp: new Date().toISOString()
            });
            
            // Keep only last 50 results
            if (results.length > 50) {
                results.splice(50);
            }
            
            localStorage.setItem(STORAGE_KEYS.RESULTS_HISTORY, JSON.stringify(results));
        }

        function loadResults() {
            const results = JSON.parse(localStorage.getItem(STORAGE_KEYS.RESULTS_HISTORY) || '[]');
            results.forEach(result => {
                if (result.type === 'image') {
                    addImageResult(result.content, result.model, result.prompt, result.size);
                } else {
                    addTextResult(result.content, result.model, result.prompt);
                }
            });
        }

        // Close modal when clicking outside
        document.getElementById('settingsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSettings();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    generateContent();
                } else if (e.key === ',') {
                    e.preventDefault();
                    openSettings();
                }
            }
            if (e.key === 'Escape') {
                closeSettings();
            }
        });
    </script>
</body>
</html>