<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Together AI Studio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f1419 100%);
            min-height: 100vh;
            color: #ffffff;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            padding: 30px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1));
            z-index: -1;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #9333ea, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .panel-section {
            padding: 25px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #3b82f6;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #e2e8f0;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #ffffff;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        /* Fix select dropdown text visibility */
        .form-group select option {
            background: #1a1a2e;
            color: #ffffff;
            padding: 8px 12px;
        }

        .form-group select optgroup {
            background: #16213e;
            color: #3b82f6;
            font-weight: 600;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .btn-generate {
            width: 100%;
            background: linear-gradient(135deg, #9333ea, #7c3aed);
            color: white;
            font-size: 16px;
            padding: 16px;
        }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(147, 51, 234, 0.4);
        }

        .btn-settings {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .model-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .model-toggle:hover {
            background: rgba(255, 255, 255, 0.08);
        }

        .model-toggle.enabled {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: #3b82f6;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        .main-content {
            padding: 30px;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .result-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .result-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .result-card-content {
            padding: 20px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 2000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: rgba(20, 20, 30, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .modal-header h2 {
            color: #3b82f6;
        }

        .close-btn {
            background: none;
            border: none;
            color: #e2e8f0;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: none;
        }

        .alert.success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }

        .alert.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .model-info {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 5px;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }

        ::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        /* Lightbox Styles */
        .lightbox {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }

        .lightbox.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .lightbox-content {
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            animation: slideIn 0.4s ease;
        }

        @keyframes slideIn {
            from { transform: scale(0.8) translateY(50px); opacity: 0; }
            to { transform: scale(1) translateY(0); opacity: 1; }
        }

        .lightbox-image {
            width: 100%;
            height: auto;
            max-height: 70vh;
            object-fit: contain;
            border-radius: 16px;
            margin-bottom: 20px;
        }

        .lightbox-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .lightbox-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .lightbox-close {
            position: absolute;
            top: -15px;
            right: -15px;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lightbox-close:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(239, 68, 68, 0.4);
        }
    </style>
</head>
<body>
    <button class="btn-settings" onclick="openSettings()">⚙️</button>

    <div class="container">
        <div class="glass header">
            <h1>🚀 Together AI Studio</h1>
            <p>Advanced AI image generation with FLUX models</p>
        </div>

        <div class="main-layout">
            <div class="sidebar">
                <div class="glass panel-section">
                    <div class="section-title">
                        🎯 Generation Settings
                    </div>
                    
                    <div class="form-group">
                        <label for="activeModel">Active Model</label>
                        <select id="activeModel" onchange="updateModelInfo()">
                            <!-- Models will be populated by JavaScript -->
                        </select>
                        <div id="modelInfo" class="model-info"></div>
                    </div>

                    <div class="form-group">
                        <label for="prompt">💭 Prompt</label>
                        <textarea id="prompt" placeholder="Describe what you want to generate..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="width">📐 Width</label>
                        <select id="width">
                            <option value="1024">1024px</option>
                            <option value="1152">1152px</option>
                            <option value="896">896px</option>
                            <option value="1216">1216px</option>
                            <option value="832">832px</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="height">📏 Height</label>
                        <select id="height">
                            <option value="1024">1024px</option>
                            <option value="896">896px</option>
                            <option value="1152">1152px</option>
                            <option value="832">832px</option>
                            <option value="1216">1216px</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="steps">🔄 Steps</label>
                        <input type="number" id="steps" min="1" max="50" value="4" placeholder="Number of inference steps">
                    </div>

                    <button class="btn btn-generate" onclick="generateContent()">
                        ⚡ Generate
                    </button>
                </div>

                <div class="glass panel-section">
                    <div class="section-title">
                        📊 Quick Actions
                    </div>
                    <button class="btn btn-secondary" onclick="clearResults()" style="width: 100%; margin-bottom: 10px;">
                        🧹 Clear Results
                    </button>
                    <button class="btn btn-secondary" onclick="openSettings()" style="width: 100%;">
                        ⚙️ Settings
                    </button>
                </div>
            </div>

            <div class="main-content glass">
                <div class="section-title">
                    🎨 Generated Content
                </div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Generating your content...</p>
                </div>

                <div class="alert" id="alert"></div>

                <div class="results-grid" id="resultsGrid">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>⚙️ Settings</h2>
                <button class="close-btn" onclick="closeSettings()">&times;</button>
            </div>

            <div class="form-group">
                <label for="apiKey">🔑 API Key</label>
                <input type="password" id="apiKey" placeholder="Enter your Together AI API key">
                <button class="btn btn-primary" onclick="saveApiKey()" style="margin-top: 10px; width: 100%;">
                    💾 Save API Key
                </button>
            </div>

            <div class="section-title" style="margin-top: 30px;">
                🤖 Available Models
            </div>
            <div id="modelsList">
                <!-- Model toggles will be populated here -->
            </div>

            <div style="margin-top: 30px;">
                <button class="btn btn-secondary" onclick="resetSettings()" style="width: 100%;">
                    🔄 Reset All Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Lightbox -->
    <div id="lightbox" class="lightbox" onclick="closeLightbox(event)">
        <div class="lightbox-content" onclick="event.stopPropagation()">
            <button class="lightbox-close" onclick="closeLightbox()">×</button>
            <img id="lightboxImage" class="lightbox-image" src="" alt="">
            <div id="lightboxInfo" class="lightbox-info"></div>
            <div class="lightbox-actions">
                <button class="btn btn-primary" onclick="downloadLightboxImage()">📥 Download</button>
                <button class="btn btn-secondary" onclick="closeLightbox()">Close</button>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'https://api.together.xyz/v1';
        const STORAGE_KEYS = {
            API_KEY: 'together_api_key',
            ENABLED_MODELS: 'together_enabled_models',
            RESULTS_HISTORY: 'together_results_history'
        };

        // Available models with their information
        const AVAILABLE_MODELS = {
            // FLUX Models - Text-to-Image
            'black-forest-labs/FLUX.1-schnell-Free': {
                name: 'FLUX.1 Schnell (Free)',
                type: 'text-to-image',
                description: '⚡ Ultra-fast free image generation',
                category: 'FLUX',
                defaultSteps: 4,
                free: true
            },
            'black-forest-labs/FLUX.1-schnell': {
                name: 'FLUX.1 Schnell (Turbo)',
                type: 'text-to-image',
                description: '🚀 Ultra-fast premium image generation',
                category: 'FLUX',
                defaultSteps: 4
            },
            'black-forest-labs/FLUX.1-dev': {
                name: 'FLUX.1 Dev',
                type: 'text-to-image',
                description: '🎨 High-quality development model',
                category: 'FLUX',
                defaultSteps: 28
            },
            'black-forest-labs/FLUX.1.1-pro': {
                name: 'FLUX.1.1 Pro',
                type: 'text-to-image',
                description: '🌟 Latest professional FLUX model',
                category: 'FLUX',
                defaultSteps: 28
            },
            'black-forest-labs/FLUX.1-pro': {
                name: 'FLUX.1 Pro',
                type: 'text-to-image',
                description: '💎 Professional quality generation',
                category: 'FLUX',
                defaultSteps: 28
            },

            // FLUX Kontext Models
            'black-forest-labs/FLUX.1-kontext-dev': {
                name: 'FLUX.1 Kontext Dev',
                type: 'text-to-image',
                description: '🎯 Context-aware image generation',
                category: 'FLUX Kontext',
                defaultSteps: 28
            },
            'black-forest-labs/FLUX.1-kontext-pro': {
                name: 'FLUX.1 Kontext Pro',
                type: 'text-to-image',
                description: '🔥 Professional context-aware model',
                category: 'FLUX Kontext',
                defaultSteps: 28
            },
            'black-forest-labs/FLUX.1-kontext-max': {
                name: 'FLUX.1 Kontext Max',
                type: 'text-to-image',
                description: '⚡ Maximum quality context model',
                category: 'FLUX Kontext',
                defaultSteps: 28
            },

            // FLUX Tools Models
            'black-forest-labs/FLUX.1-canny': {
                name: 'FLUX.1 Canny',
                type: 'image-to-image',
                description: '✏️ Edge-guided image generation',
                category: 'FLUX Tools',
                defaultSteps: 28
            },
            'black-forest-labs/FLUX.1-depth': {
                name: 'FLUX.1 Depth',
                type: 'image-to-image',
                description: '🏔️ Depth-guided image generation',
                category: 'FLUX Tools',
                defaultSteps: 28
            },
            'black-forest-labs/FLUX.1-redux': {
                name: 'FLUX.1 Redux',
                type: 'image-to-image',
                description: '🔄 Image variation and transformation',
                category: 'FLUX Tools',
                defaultSteps: 28
            }
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            populateModels();
            loadResults();
        });

        // Settings management
        function loadSettings() {
            const apiKey = localStorage.getItem(STORAGE_KEYS.API_KEY);
            if (apiKey) {
                document.getElementById('apiKey').value = apiKey;
            }

            const enabledModels = JSON.parse(localStorage.getItem(STORAGE_KEYS.ENABLED_MODELS) || '{}');

            // Enable some models by default if none are set
            if (Object.keys(enabledModels).length === 0) {
                enabledModels['black-forest-labs/FLUX.1-schnell-Free'] = true;
                enabledModels['black-forest-labs/FLUX.1-schnell'] = true;
                enabledModels['black-forest-labs/FLUX.1-dev'] = true;
                enabledModels['black-forest-labs/FLUX.1.1-pro'] = true;
                localStorage.setItem(STORAGE_KEYS.ENABLED_MODELS, JSON.stringify(enabledModels));
            }
        }

        function saveApiKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            if (apiKey) {
                localStorage.setItem(STORAGE_KEYS.API_KEY, apiKey);
                showAlert('🔑 API Key saved successfully!', 'success');
            } else {
                showAlert('❌ Please enter a valid API key.', 'error');
            }
        }

        function populateModels() {
            const enabledModels = JSON.parse(localStorage.getItem(STORAGE_KEYS.ENABLED_MODELS) || '{}');
            const modelsList = document.getElementById('modelsList');
            const activeModelSelect = document.getElementById('activeModel');

            // Clear existing options
            modelsList.innerHTML = '';
            activeModelSelect.innerHTML = '';

            // Group models by category
            const categories = {};
            Object.entries(AVAILABLE_MODELS).forEach(([modelId, model]) => {
                if (!categories[model.category]) {
                    categories[model.category] = [];
                }
                categories[model.category].push([modelId, model]);
            });

            // Create optgroups for each category
            Object.entries(categories).forEach(([category, models]) => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = `${getCategoryIcon(category)} ${category}`;

                models.forEach(([modelId, model]) => {
                    // Create toggle in settings
                    const toggleDiv = document.createElement('div');
                    toggleDiv.className = `model-toggle ${enabledModels[modelId] ? 'enabled' : ''}`;
                    toggleDiv.innerHTML = `
                        <div>
                            <div style="font-weight: 600;">${model.name}</div>
                            <div style="font-size: 12px; opacity: 0.7;">${model.description}</div>
                            <div style="font-size: 10px; opacity: 0.5; margin-top: 2px;">${model.type} • ${model.defaultSteps} steps${model.free ? ' • FREE' : ''}</div>
                        </div>
                        <div class="toggle-switch ${enabledModels[modelId] ? 'active' : ''}" onclick="toggleModel('${modelId}')"></div>
                    `;
                    modelsList.appendChild(toggleDiv);

                    // Add to active model select if enabled
                    if (enabledModels[modelId]) {
                        const option = document.createElement('option');
                        option.value = modelId;
                        option.textContent = model.name;
                        optgroup.appendChild(option);
                    }
                });

                if (optgroup.children.length > 0) {
                    activeModelSelect.appendChild(optgroup);
                }
            });

            updateModelInfo();
        }

        function getCategoryIcon(category) {
            const icons = {
                'FLUX': '⚡',
                'FLUX Kontext': '🎯',
                'FLUX Tools': '🛠️'
            };
            return icons[category] || '🤖';
        }

        function toggleModel(modelId) {
            const enabledModels = JSON.parse(localStorage.getItem(STORAGE_KEYS.ENABLED_MODELS) || '{}');
            enabledModels[modelId] = !enabledModels[modelId];
            localStorage.setItem(STORAGE_KEYS.ENABLED_MODELS, JSON.stringify(enabledModels));
            populateModels();
        }

        function updateModelInfo() {
            const activeModel = document.getElementById('activeModel').value;
            const modelInfo = document.getElementById('modelInfo');
            const stepsInput = document.getElementById('steps');

            if (activeModel && AVAILABLE_MODELS[activeModel]) {
                const model = AVAILABLE_MODELS[activeModel];
                modelInfo.textContent = model.description;
                stepsInput.value = model.defaultSteps;
            } else {
                modelInfo.textContent = 'Select a model to see information';
                stepsInput.value = 4;
            }
        }

        function resetSettings() {
            if (confirm('Are you sure you want to reset all settings? This will clear your API key and model preferences.')) {
                Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key));
                document.getElementById('apiKey').value = '';
                loadSettings();
                populateModels();
                showAlert('🔄 Settings reset successfully!', 'success');
            }
        }

        // Modal management
        function openSettings() {
            document.getElementById('settingsModal').classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeSettings() {
            document.getElementById('settingsModal').classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Content generation
        async function generateContent() {
            const apiKey = localStorage.getItem(STORAGE_KEYS.API_KEY);
            const activeModel = document.getElementById('activeModel').value;
            const prompt = document.getElementById('prompt').value.trim();
            const width = parseInt(document.getElementById('width').value);
            const height = parseInt(document.getElementById('height').value);
            const steps = parseInt(document.getElementById('steps').value);

            if (!apiKey) {
                showAlert('🔑 Please set your API key in settings first.', 'error');
                return;
            }

            if (!activeModel) {
                showAlert('🤖 Please enable at least one model in settings.', 'error');
                return;
            }

            if (!prompt) {
                showAlert('💭 Please enter a prompt.', 'error');
                return;
            }

            const loading = document.getElementById('loading');
            loading.classList.add('active');

            try {
                // Prepare request body
                const requestBody = {
                    model: activeModel,
                    prompt: prompt,
                    width: width,
                    height: height,
                    steps: steps,
                    response_format: 'b64_json'
                };

                // Disable safety checker for all models except FLUX Schnell Free and FLUX Pro
                const modelsWithSafetyChecker = [
                    'black-forest-labs/FLUX.1-schnell-Free',
                    'black-forest-labs/FLUX.1-pro'
                ];

                if (!modelsWithSafetyChecker.includes(activeModel)) {
                    requestBody.disable_safety_checker = true;
                }

                const response = await fetch(`${API_BASE_URL}/images/generations`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`API request failed: ${response.status} ${response.statusText}${errorData.error ? ' - ' + errorData.error.message : ''}`);
                }

                const data = await response.json();

                if (data.data && data.data.length > 0) {
                    const imageBase64 = data.data[0].b64_json;
                    const imageUrl = `data:image/png;base64,${imageBase64}`;

                    addImageResult(imageUrl, activeModel, prompt, `${width}x${height}`);
                    showAlert('✨ Image generated successfully!', 'success');
                } else {
                    throw new Error('No image data received from API.');
                }

            } catch (error) {
                console.error('Generation error:', error);
                showAlert(`❌ Generation failed: ${error.message}`, 'error');
            } finally {
                loading.classList.remove('active');
            }
        }

        function addImageResult(imageUrl, model, prompt, size) {
            const resultsGrid = document.getElementById('resultsGrid');
            const timestamp = new Date().toISOString();
            const displayTimestamp = new Date().toLocaleString();
            const modelName = AVAILABLE_MODELS[model] ? AVAILABLE_MODELS[model].name : model;

            const resultCard = document.createElement('div');
            resultCard.className = 'result-card';
            resultCard.innerHTML = `
                <img src="${imageUrl}" alt="Generated image" onclick="openLightbox('${imageUrl}', '${modelName}', '${prompt.replace(/'/g, "\\'")}', '${size}', '${displayTimestamp}')" style="cursor: pointer;">
                <div class="result-card-content">
                    <div style="font-weight: 600; margin-bottom: 8px;">${modelName}</div>
                    <div style="font-size: 12px; opacity: 0.7; margin-bottom: 10px;">${size}</div>
                    <div style="font-size: 14px; margin-bottom: 15px;">${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}</div>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-primary" onclick="downloadImage('${imageUrl}', '${model}_${Date.now()}.png')" style="flex: 1;">📥 Download</button>
                        <button class="btn btn-secondary" onclick="removeResult(this, '${timestamp}')">🗑️</button>
                    </div>
                </div>
            `;
            resultsGrid.insertBefore(resultCard, resultsGrid.firstChild);
            saveResult('image', imageUrl, model, prompt, size, timestamp);
        }

        // Global variable for lightbox
        let currentLightboxData = null;

        // Lightbox functionality
        function openLightbox(imageUrl, model, prompt, size, timestamp) {
            const lightbox = document.getElementById('lightbox');
            const lightboxImage = document.getElementById('lightboxImage');
            const lightboxInfo = document.getElementById('lightboxInfo');

            lightboxImage.src = imageUrl;
            lightboxInfo.innerHTML = `
                <div><strong>🤖 Model:</strong> ${model}</div>
                <div><strong>📐 Size:</strong> ${size}</div>
                <div><strong>💭 Prompt:</strong> ${prompt}</div>
                <div><strong>🕒 Generated:</strong> ${timestamp}</div>
            `;

            currentLightboxData = {
                url: imageUrl,
                filename: `together_${model.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.png`
            };

            lightbox.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeLightbox(event) {
            if (event && event.target !== event.currentTarget) return;

            const lightbox = document.getElementById('lightbox');
            lightbox.classList.remove('active');
            document.body.style.overflow = 'auto';
            currentLightboxData = null;
        }

        function downloadLightboxImage() {
            if (currentLightboxData) {
                downloadImage(currentLightboxData.url, currentLightboxData.filename);
            }
        }

        // Utility functions
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert ${type}`;
            alert.style.display = 'block';

            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        function clearResults() {
            if (confirm('Are you sure you want to clear all results?')) {
                document.getElementById('resultsGrid').innerHTML = '';
                localStorage.removeItem(STORAGE_KEYS.RESULTS_HISTORY);
                showAlert('🧹 Results cleared!', 'success');
            }
        }

        function removeResult(buttonElement, timestamp) {
            // Remove from UI
            const resultCard = buttonElement.closest('.result-card');
            resultCard.remove();

            // Remove from storage
            const results = JSON.parse(localStorage.getItem(STORAGE_KEYS.RESULTS_HISTORY) || '[]');
            const filteredResults = results.filter(result => result.timestamp !== timestamp);
            localStorage.setItem(STORAGE_KEYS.RESULTS_HISTORY, JSON.stringify(filteredResults));

            showAlert('🗑️ Result removed!', 'success');
        }

        function downloadImage(url, filename) {
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        function saveResult(type, content, model, prompt, size = null, timestamp = null) {
            const results = JSON.parse(localStorage.getItem(STORAGE_KEYS.RESULTS_HISTORY) || '[]');
            results.unshift({
                type,
                content,
                model,
                prompt,
                size,
                timestamp: timestamp || new Date().toISOString()
            });

            // Keep only last 50 results
            if (results.length > 50) {
                results.splice(50);
            }

            localStorage.setItem(STORAGE_KEYS.RESULTS_HISTORY, JSON.stringify(results));
        }

        function loadResults() {
            const resultsGrid = document.getElementById('resultsGrid');
            // Clear existing results to prevent duplicates on refresh
            resultsGrid.innerHTML = '';

            const results = JSON.parse(localStorage.getItem(STORAGE_KEYS.RESULTS_HISTORY) || '[]');
            results.forEach(result => {
                if (result.type === 'image') {
                    // Add result without saving again to prevent duplicate storage
                    addImageResultFromHistory(result.content, result.model, result.prompt, result.size, result.timestamp);
                }
            });
        }

        function addImageResultFromHistory(imageUrl, model, prompt, size, timestamp) {
            const resultsGrid = document.getElementById('resultsGrid');
            const displayTimestamp = timestamp ? new Date(timestamp).toLocaleString() : new Date().toLocaleString();
            const modelName = AVAILABLE_MODELS[model] ? AVAILABLE_MODELS[model].name : model;

            const resultCard = document.createElement('div');
            resultCard.className = 'result-card';
            resultCard.innerHTML = `
                <img src="${imageUrl}" alt="Generated image" onclick="openLightbox('${imageUrl}', '${modelName}', '${prompt.replace(/'/g, "\\'")}', '${size}', '${displayTimestamp}')" style="cursor: pointer;">
                <div class="result-card-content">
                    <div style="font-weight: 600; margin-bottom: 8px;">${modelName}</div>
                    <div style="font-size: 12px; opacity: 0.7; margin-bottom: 10px;">${size}</div>
                    <div style="font-size: 14px; margin-bottom: 15px;">${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}</div>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-primary" onclick="downloadImage('${imageUrl}', '${model}_${Date.now()}.png')" style="flex: 1;">📥 Download</button>
                        <button class="btn btn-secondary" onclick="removeResult(this, '${timestamp || Date.now()}')">🗑️</button>
                    </div>
                </div>
            `;
            resultsGrid.appendChild(resultCard);
        }

        // Close modal when clicking outside
        document.getElementById('settingsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSettings();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    generateContent();
                } else if (e.key === ',') {
                    e.preventDefault();
                    openSettings();
                }
            }
            if (e.key === 'Escape') {
                const lightbox = document.getElementById('lightbox');
                if (lightbox.classList.contains('active')) {
                    closeLightbox();
                } else {
                    closeSettings();
                }
            }
        });
    </script>
</body>
</html>
